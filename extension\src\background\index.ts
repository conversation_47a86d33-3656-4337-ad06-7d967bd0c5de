/**
 * EchoSync Background Script
 * Service Worker - 负责数据库操作的中转和消息处理
 */

import { dexieDatabase } from '../lib/database/dexie'
import { chatHistoryService } from '../lib/service/chatHistoryDexie'
import { platformService } from '../lib/service/platformDexie'
import { StorageService } from '../lib/service/storage'
import { MessagingService } from '../lib/service/messaging'
import { MessageType, ChromeMessage } from '../types'

console.log('【EchoSync】Service Worker loaded')

// 初始化数据库
let dbInitialized = false
async function initDatabase() {
  if (dbInitialized) return

  try {
    console.log('【EchoSync】Initializing database...')
    await dexieDatabase.initialize()
    dbInitialized = true
    console.log('【EchoSync】Database initialized successfully')
  } catch (error) {
    console.error('【EchoSync】Database initialization failed:', error)
    throw error
  }
}

// 扩展安装时初始化
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('【EchoSync】Extension installed:', details.reason)

  try {
    await initDatabase()

    if (details.reason === 'install') {
      // 首次安装，设置默认配置
      const settings = await StorageService.getSettings()
      await StorageService.saveSettings(settings)

      // 打开欢迎页面
      chrome.tabs.create({
        url: chrome.runtime.getURL('options/index.html?welcome=true')
      })
    }
  } catch (error) {
    console.error('【EchoSync】Installation setup failed:', error)
  }
})

// Service Worker启动时初始化数据库
chrome.runtime.onStartup.addListener(async () => {
  console.log('【EchoSync】Service Worker started')
  try {
    await initDatabase()
  } catch (error) {
    console.error('【EchoSync】Startup initialization failed:', error)
  }
})

// 消息处理
MessagingService.onMessage(async (message: ChromeMessage, sender, sendResponse) => {
  console.log('【EchoSync】Background received message:', message.type)

  try {
    // 确保数据库已初始化
    await initDatabase()

    switch (message.type) {
      // 聊天历史相关操作
      case MessageType.DB_CHAT_HISTORY_CREATE:
        const createResult = await chatHistoryService.create(message.payload)
        sendResponse(createResult)
        break

      case MessageType.DB_CHAT_HISTORY_GET_LIST:
        const listResult = await chatHistoryService.getList(message.payload)
        sendResponse(listResult)
        break

      case MessageType.DB_CHAT_HISTORY_GET_UNIQUE:
        const uniqueResult = await chatHistoryService.getUniqueChats(message.payload)
        sendResponse(uniqueResult)
        break

      case MessageType.DB_CHAT_HISTORY_SEARCH:
        const searchResult = await chatHistoryService.search(message.payload.searchTerm, message.payload.params)
        sendResponse(searchResult)
        break

      case MessageType.DB_CHAT_HISTORY_UPDATE:
        const updateResult = await chatHistoryService.update(message.payload.id, message.payload.data)
        sendResponse(updateResult)
        break

      case MessageType.DB_CHAT_HISTORY_DELETE:
        const deleteResult = await chatHistoryService.delete(message.payload.id)
        sendResponse(deleteResult)
        break

      case MessageType.DB_CHAT_HISTORY_GET_BY_UID:
        const uidResult = await chatHistoryService.getByChatUid(message.payload.chatUid)
        sendResponse(uidResult)
        break

      // 平台相关操作
      case MessageType.DB_PLATFORM_GET_BY_NAME:
        const platformByNameResult = await platformService.getByName(message.payload.name)
        sendResponse(platformByNameResult)
        break

      case MessageType.DB_PLATFORM_GET_BY_DOMAIN:
        const platformByDomainResult = await platformService.findByDomain(message.payload.hostname)
        sendResponse(platformByDomainResult)
        break

      case MessageType.DB_PLATFORM_GET_LIST:
        const platformListResult = await platformService.getAll()
        sendResponse(platformListResult)
        break

      // 兼容旧接口
      case MessageType.GET_HISTORY:
        const historyResult = await chatHistoryService.getUniqueChats({ limit: 100 })
        if (historyResult.success) {
          sendResponse({ success: true, data: historyResult.data })
        } else {
          sendResponse({ success: false, error: historyResult.error })
        }
        break

      case MessageType.SAVE_CONVERSATION:
        await StorageService.addConversation(message.payload)
        sendResponse({ success: true })
        break

      case MessageType.UPDATE_SETTINGS:
        await StorageService.saveSettings(message.payload)
        sendResponse({ success: true })
        break

      // 提示词同步和捕获
      case MessageType.SYNC_PROMPT:
        await handleSyncPrompt(message.payload, sender)
        sendResponse({ success: true })
        break

      case MessageType.CAPTURE_PROMPT:
        await handleCapturePrompt(message.payload)
        sendResponse({ success: true })
        break

      default:
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  } catch (error) {
    console.error('【EchoSync】Message handling error:', error)
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// 辅助函数：获取或创建平台
async function getPlatform(platformName: string) {
  if (!platformName) return null

  const name = platformName.charAt(0).toUpperCase() + platformName.slice(1).toLowerCase()
  let result = await platformService.getByName(name)

  if (result.success) return result.data

  // 尝试创建平台
  const urls: Record<string, string> = {
    'Deepseek': 'https://chat.deepseek.com',
    'Chatgpt': 'https://chat.openai.com',
    'Claude': 'https://claude.ai',
    'Gemini': 'https://gemini.google.com',
    'Kimi': 'https://kimi.moonshot.cn'
  }

  if (urls[name]) {
    const createResult = await platformService.create({ name, url: urls[name] })
    return createResult.success ? createResult.data : null
  }

  return null
}

// 辅助函数：同步到其他标签页
async function syncToOtherTabs(content: string, excludeTabId?: number) {
  const tabs = await chrome.tabs.query({
    url: ['https://chat.openai.com/*', 'https://chat.deepseek.com/*', 'https://claude.ai/*',
          'https://gemini.google.com/*', 'https://kimi.moonshot.cn/*']
  })

  const promises = tabs
    .filter(tab => tab.id !== excludeTabId)
    .map(tab => tab.id ? MessagingService.sendToContentScript(
      tab.id, MessageType.INJECT_PROMPT, { prompt: content }
    ).catch(() => {}) : Promise.resolve())

  await Promise.all(promises)
}

// 处理提示词同步
async function handleSyncPrompt(promptData: any, sender: any) {
  try {
    const platform = await getPlatform(promptData.platform)
    if (!platform) return

    // 保存提示词
    const result = await chatHistoryService.create({
      chat_prompt: promptData.content,
      platform_id: platform.id!,
      chat_uid: Date.now().toString(),
      create_time: promptData.timestamp || Date.now()
    })

    if (result.success) {
      console.log('【EchoSync】Prompt saved:', result.data?.id)

      // 检查是否启用同步
      const settings = await StorageService.getSettings()
      if (settings.syncEnabled) {
        await syncToOtherTabs(promptData.content, sender.tab?.id)
      }
    }
  } catch (error) {
    console.error('【EchoSync】Sync prompt error:', error)
  }
}

// 处理提示词捕获
async function handleCapturePrompt(data: any) {
  try {
    const platform = await getPlatform(data.platform)
    if (!platform) return

    const result = await chatHistoryService.create({
      chat_prompt: data.content,
      platform_id: platform.id!,
      chat_uid: Date.now().toString(),
      create_time: Date.now()
    })

    if (result.success) {
      console.log('【EchoSync】Prompt captured:', result.data?.id)

      // 通知popup更新
      chrome.runtime.sendMessage({
        type: 'PROMPT_CAPTURED',
        payload: { id: result.data?.id, content: data.content, platform: data.platform, timestamp: Date.now() }
      }).catch(() => {})
    }
  } catch (error) {
    console.error('【EchoSync】Capture prompt error:', error)
  }
}

// 监听标签页更新，注入content script
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    const supportedDomains = ['chat.openai.com', 'chat.deepseek.com', 'claude.ai', 'gemini.google.com', 'kimi.moonshot.cn']
    const isSupported = supportedDomains.some(domain => tab.url && tab.url.includes(domain))

    if (isSupported) {
      chrome.scripting.executeScript({ target: { tabId }, files: ['content/index.js'] }).catch(() => {})
    }
  }
})

// 处理快捷键
chrome.commands.onCommand.addListener(async (command) => {
  const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
  if (!tab.id) return

  if (command === 'open-popup') {
    chrome.action.openPopup()
  } else if (command === 'quick-sync') {
    MessagingService.sendToContentScript(tab.id, MessageType.CAPTURE_PROMPT, {}).catch(() => {})
  }
})
